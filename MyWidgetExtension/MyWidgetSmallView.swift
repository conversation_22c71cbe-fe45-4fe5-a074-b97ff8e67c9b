//
//  MyWidgetSmallView.swift
//  MyWidgetExtensionExtension
//
//  Created by lifubing on 2025/9/21.
//

import WidgetKit
import SwiftUI
import AppIntents


struct MyWidgetSmallView: View {
    var entry: PhotoEntry
    
    var body: some View {
        
        GeometryReader { geometry in
            
            let sizeMax = 1.3
            let offsetSize = 0.14
            let fullWidth = geometry.size.width * 1.1
            ZStack {
                // 背景图片或渐变，完全填满Widget
                if let photo = entry.photo, let photoIdentifier = entry.photoIdentifier, !photoIdentifier.isEmpty {
                    
                    // 添加点击照片的功能 - 使用Link打开APP
                    Link(destination: URL(string: "photoclear://openPhoto?id=\(photoIdentifier)")!) {
                        Image(uiImage: photo)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: geometry.size.width * sizeMax, height: geometry.size.height * sizeMax)
                            .offset(x: -geometry.size.width * offsetSize, y: -geometry.size.height * offsetSize)
                    }
                    
                } else if let photo = entry.photo {
                    // 有照片但没有标识符，只显示图片不可点击
                    Image(uiImage: photo)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: geometry.size.width * sizeMax, height: geometry.size.height * sizeMax)
                        .offset(x: -geometry.size.width * offsetSize, y: -geometry.size.height * offsetSize)
                } else {
                    // 默认背景
                    Link(destination: URL(string: "photoclear://openPhoto")!) {
                        Text("所有照片都已操作过，请点击打开APP更新缓存")
                            .font(.system(size: 12))
                            .multilineTextAlignment(.center)
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                    }
                }
                
                // 时间显示区域 - 放在左下角
//                VStack {
//                    Spacer()
//                    HStack {
//                        if let photoMetadata = entry.photoMetadata,
//                           let creationDate = photoMetadata.creationDate {
//                            Text(formatPhotoDate(creationDate))
//                                .font(.system(size: 10, weight: .medium))
//                                .foregroundColor(.white)
//                                .padding(.horizontal, 6)
//                                .padding(.vertical, 3)
//                                .background(Color.black.opacity(0.2))
//                                .cornerRadius(4)
//                                .shadow(radius: 2)
//                        }
//                        Spacer()
//                    }
//                }
//                .frame(width: geometry.size.width * 1.1, height: geometry.size.height * 1)
//                .offset(x: -geometry.size.width * offsetSize, y: -geometry.size.height * offsetSize)
                
                // 按钮区域 - 放在右上角
                VStack {
                    Spacer()
                    HStack(spacing: 8) {
//                        Spacer()
                        
//                        VStack {
//                            
//                            Spacer()
                            
                            if let photoIdentifier = entry.photoIdentifier {
                                
                                Spacer()
//
                                Button(intent: ArchivePhotoIntent(photoIdentifier: photoIdentifier)) {
                                    Image(systemName: "archivebox")
                                        .font(.system(size: 14))
                                        .bold()
                                        .foregroundColor(.white)
                                        .frame(width: fullWidth / 3, height: 32) // 设置正方形尺寸
                                        .shadow(color: .black, radius: 8)
                                }
                                .buttonStyle(PlainButtonStyle())
                                
                               
                                // 标记删除按钮
                                Button(intent: MarkForDeletionIntent(photoIdentifier: photoIdentifier)) {
                                    Image(systemName: "trash")
                                        .font(.system(size: 14))
                                        .bold()
                                        .foregroundColor(.white)
                                        .frame(width: fullWidth / 3, height: 32) // 设置正方形尺寸
                                        .shadow(color: .black, radius: 8)
                                }
                                .buttonStyle(PlainButtonStyle())
                                
                                // 切换到下一张照片按钮
                                Button(intent: NextPhotoIntent(photoIdentifier:photoIdentifier)) {
//                                    Image(systemName: "chevron.right")
//                                    Image(systemName: "arrowshape.forward")
                                    Image(systemName: "arrow.forward")
                                        .font(.system(size: 15))
                                        .bold()
                                        .foregroundColor(.white)
                                        .frame(width: fullWidth / 3, height: 32) // 设置正方形尺寸
                                        .shadow(color: .black, radius: 8)
                                }
                                .buttonStyle(PlainButtonStyle())
                                
                                Spacer()
                            }
                    }
                }

                .frame(width: geometry.size.width * 1, height: geometry.size.height * 1.1)
                .offset(x: -geometry.size.width * offsetSize, y: -geometry.size.height * 0.1)
            }
        }
    }
}
